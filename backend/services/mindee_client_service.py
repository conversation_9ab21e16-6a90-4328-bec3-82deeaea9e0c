"""
Mindee API service using the official Mindee Python client library.
"""
import os
import logging
import json
from typing import Dict, Any, Optional, List, Tuple

from mindee import Client
# # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # from mindee.parsing.common import Prediction
from dotenv import load_dotenv

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("ocr.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('mindee')

class MindeeClientService:
    """
    Service for interacting with the Mindee API using the official Python client.
    """
    def __init__(self):
        """Initialize the Mindee client service."""
        # Load environment variables
        load_dotenv()

        # Get API key from environment
        self.api_key = os.environ.get('MINDEE_API_KEY')
        if not self.api_key:
            logger.error("MINDEE_API_KEY not found in environment variables")
            raise ValueError("MINDEE_API_KEY not found in environment variables")

        # Initialize the Mindee client
        self.client = Client(api_key=self.api_key)

        # Define vendor-specific endpoints
        self.vendor_endpoints = {
            'bova': self.client.create_endpoint(
                account_name="avmaman7",
                endpoint_name="bova",
                version="1"
            ),
            'kast': self.client.create_endpoint(
                account_name="avmaman7",
                endpoint_name="kast",
                version="1"
            ),
            'dekalb': self.client.create_endpoint(
                account_name="avmaman7",
                endpoint_name="dekalb_produce",
                version="1"
            ),
        }

        logger.info("Mindee client service initialized")

    def process_invoice(self, image_path: str, vendor: str = 'generic') -> Dict[str, Any]:
        """
        Process an invoice image using the Mindee API.

        Args:
            image_path: Path to the invoice image file
            vendor: Vendor type (generic, bova, kast, dekalb)

        Returns:
            dict: Structured data extracted from the invoice
        """
        try:
            logger.info(f"Processing invoice with Mindee client (vendor: {vendor})")

            # Load the document from path
            input_doc = self.client.source_from_path(image_path)

            if vendor.lower() == 'generic':
                # Use the generic invoice API
                logger.info("Using generic invoice API")
                # Use string identifier instead of product.InvoiceV4
                result = self.client.parse("invoices", input_doc)
                return self._parse_generic_response(result)
            else:
                # Use vendor-specific API if available
                if vendor.lower() in self.vendor_endpoints:
                    logger.info(f"Using {vendor} custom API")
                    endpoint = self.vendor_endpoints[vendor.lower()]

                    # Parse with the custom endpoint
                    result = self.client.enqueue_and_parse(
                        "custom",
                        input_doc,
                        endpoint=endpoint
                    )

                    # Parse based on vendor
                    if vendor.lower() == 'bova':
                        return self._parse_bova_response(result)
                    elif vendor.lower() == 'kast':
                        return self._parse_kast_response(result)
                    elif vendor.lower() == 'dekalb':
                        return self._parse_dekalb_response(result)
                else:
                    logger.warning(f"Vendor {vendor} not found, falling back to generic API")
                    result = self.client.parse("invoices", input_doc)
                    return self._parse_generic_response(result)

        except Exception as e:
            logger.error(f"Error processing invoice with Mindee client: {str(e)}")
            return {
                "success": False,
                "error": f"Error processing invoice: {str(e)}",
                "items": []
            }

    def _parse_generic_response(self, result):
        """
        Parse response from generic invoice API.

        Args:
            result: Result from Mindee API

        Returns:
            dict: Structured data in our application's format
        """
        try:
            # Log the document summary for debugging
            logger.info(f"Generic API response summary: {result.document}")

            # Extract invoice data
            prediction = result.document.inference.prediction

            # Handle invoice number
            invoice_number = None
            if hasattr(prediction.invoice_number, 'value'):
                invoice_number = prediction.invoice_number.value

            # Handle date
            date = None
            if hasattr(prediction.date, 'value'):
                date = prediction.date.value

            # Handle due date
            due_date = None
            if hasattr(prediction.due_date, 'value'):
                due_date = prediction.due_date.value

            # Handle total amount
            total_amount = None
            if hasattr(prediction.total_amount, 'value'):
                total_amount = prediction.total_amount.value

            # Get supplier information
            supplier_name = None
            if hasattr(prediction.supplier_name, 'value'):
                supplier_name = prediction.supplier_name.value

            # Extract line items
            line_items = []
            if hasattr(prediction, 'line_items'):
                for item in prediction.line_items:
                    # Initialize with default values
                    description = ""
                    quantity = 0
                    unit_price = 0
                    total_amount_item = 0

                    # Extract values if available
                    if hasattr(item, 'description') and hasattr(item.description, 'value'):
                        description = item.description.value or ""

                    if hasattr(item, 'quantity') and hasattr(item.quantity, 'value'):
                        quantity = item.quantity.value or 0

                    if hasattr(item, 'unit_price') and hasattr(item.unit_price, 'value'):
                        unit_price = item.unit_price.value or 0

                    if hasattr(item, 'total_amount') and hasattr(item.total_amount, 'value'):
                        total_amount_item = item.total_amount.value or 0

                    line_items.append({
                        "description": description,
                        "quantity": quantity,
                        "unit_price": unit_price,
                        "amount": total_amount_item
                    })

            logger.info(f"Successfully extracted {len(line_items)} line items from generic invoice API")

            return {
                "success": True,
                "invoice_number": invoice_number,
                "date": date,
                "due_date": due_date,
                "total_amount": total_amount,
                "vendor": supplier_name,
                "items": line_items
            }

        except Exception as e:
            logger.error(f"Error parsing generic invoice response: {str(e)}")
            return {
                "success": False,
                "error": f"Error parsing invoice: {str(e)}",
                "items": []
            }

    def _parse_bova_response(self, result):
        """
        Parse response from Bova custom API.

        Args:
            result: Result from Mindee API

        Returns:
            dict: Structured data in our application's format
        """
        try:
            # Log the document summary for debugging
            logger.info(f"Bova API response summary: {result.document}")

            # Get the prediction fields
            fields = result.document.inference.prediction.fields

            # Extract invoice data
            invoice_number = self._get_field_value(fields, 'invoice_number')

            # For Bova, try different date field names
            date = self._get_field_value(fields, 'invoice_date')
            if not date:
                date = self._get_field_value(fields, 'date')

            due_date = self._get_field_value(fields, 'due_date')

            # For Bova, try different total amount field names
            total_amount = self._get_field_value(fields, 'transaction_amount')
            if not total_amount:
                total_amount = self._get_field_value(fields, 'total_amount')

            # Default vendor name
            vendor_name = "Bova"

            # Extract line items
            line_items = []

            # Try to get line items from different possible field names
            line_items_field = None

            # Log all available fields for debugging
            logger.info(f"Available fields: {list(fields.keys()) if isinstance(fields, dict) else dir(fields)}")

            for field_name in ['line_items', 'lineItems', 'items']:
                if isinstance(fields, dict) and field_name in fields:
                    line_items_field = fields[field_name]
                    logger.info(f"Found line items in field: {field_name}")
                    break

            # If we didn't find line items in the fields dictionary, try to access it as an attribute
            if line_items_field is None:
                for field_name in ['line_items', 'lineItems', 'items']:
                    if hasattr(fields, field_name):
                        line_items_field = getattr(fields, field_name)
                        logger.info(f"Found line items in attribute: {field_name}")
                        break

            # Process line items if we found them
            if line_items_field:
                # Log the type of line_items_field for debugging
                logger.info(f"Line items field type: {type(line_items_field)}")
                logger.info(f"Line items field value: {line_items_field}")

                # Convert to list if it's not already
                if not isinstance(line_items_field, list):
                    # Try to convert to list if possible
                    try:
                        # If it's a GeneratedV1LineItemField, we need to access its value
                        if hasattr(line_items_field, 'values'):
                            logger.info("Found values attribute in line_items_field")
                            line_items_field = line_items_field.values
                        elif hasattr(line_items_field, 'value'):
                            logger.info("Found value attribute in line_items_field")
                            line_items_field = line_items_field.value

                        # Now try to convert to list
                        if not isinstance(line_items_field, list):
                            line_items_field = list(line_items_field)
                    except Exception as e:
                        logger.warning(f"Could not convert line_items to list: {str(e)}")
                        line_items_field = []

                logger.info(f"Found {len(line_items_field)} line items in Bova response")

                for i, item in enumerate(line_items_field):
                    # For Bova vendor, use the specific field names we've identified from testing
                    # Description: descripiton (note the typo in the API field name)
                    # Quantity: quantity_shipped
                    # Unit Price: selling_price
                    # Amount: extensions

                    # Get description from descripiton field (primary field for Bova)
                    description = self._get_field_value(item, 'descripiton')  # Note: API has a typo in the field name
                    if not description:
                        # Try alternative field names if the primary one is not available
                        for desc_field in ['description', 'product_name', 'name', 'item_name']:
                            description = self._get_field_value(item, desc_field)
                            if description:
                                logger.info(f"Found description in alternative field: {desc_field}")
                                break

                    # If description is still empty, try to use product_code or other fields
                    if not description:
                        # Try alternative field names that might contain description
                        alt_fields = ['product_description', 'item_description', 'name', 'product_name', 'item_name']
                        for field in alt_fields:
                            description = self._get_field_value(item, field)
                            if description:
                                break

                    # If still no description, try to use product_code or other identifiers
                    if not description:
                        product_code = self._get_field_value(item, 'product_code')
                        item_code = self._get_field_value(item, 'item_code')
                        sku = self._get_field_value(item, 'sku')

                        if product_code:
                            description = f"Product {product_code}"
                        elif item_code:
                            description = f"Item {item_code}"
                        elif sku:
                            description = f"SKU {sku}"
                        else:
                            # Log this case for debugging and skip items without descriptions
                            logger.warning(f"Bova line item {i+1} has no description or identifiers. Item data: {item}")
                            continue

                    # Get quantity from quantity_shipped field (primary field for Bova)
                    quantity = self._get_field_value(item, 'quantity_shipped')
                    if quantity is None:
                        # Try alternative field names if the primary one is not available
                        for qty_field in ['quantity_ordered', 'quantity', 'qty']:
                            quantity = self._get_field_value(item, qty_field)
                            if quantity is not None:
                                logger.info(f"Found quantity in alternative field: {qty_field}")
                                break

                    # Get unit price from selling_price field (primary field for Bova)
                    unit_price = self._get_field_value(item, 'selling_price')
                    if unit_price is None:
                        # Try alternative field names if the primary one is not available
                        for price_field in ['unit_price', 'price', 'unit_cost']:
                            unit_price = self._get_field_value(item, price_field)
                            if unit_price is not None:
                                logger.info(f"Found unit price in alternative field: {price_field}")
                                break

                    # Get amount from extensions field (primary field for Bova)
                    amount = self._get_field_value(item, 'extensions')
                    if amount is None:
                        # Try alternative field names if the primary one is not available
                        for amount_field in ['amount', 'total_amount', 'extension', 'total']:
                            amount = self._get_field_value(item, amount_field)
                            if amount is not None:
                                logger.info(f"Found amount in alternative field: {amount_field}")
                                break

                    # Convert to appropriate types
                    try:
                        quantity = float(quantity) if quantity else 0
                        unit_price = float(unit_price) if unit_price else 0
                        amount = float(amount) if amount else 0

                        # If quantity is 0 but we have unit_price and amount, calculate quantity
                        if quantity == 0 and unit_price > 0 and amount > 0:
                            # Calculate quantity as amount / unit_price, rounded to nearest integer
                            calculated_quantity = round(amount / unit_price)
                            logger.info(f"Calculated quantity: {calculated_quantity} (amount: {amount} / unit_price: {unit_price})")
                            quantity = calculated_quantity
                    except (ValueError, TypeError):
                        logger.warning(f"Error converting line item values to float: {item}")

                    line_items.append({
                        "description": description,
                        "quantity": quantity,
                        "unit_price": unit_price,
                        "amount": amount
                    })

            logger.info(f"Successfully extracted {len(line_items)} line items from Bova invoice")

            return {
                "success": True,
                "invoice_number": invoice_number,
                "date": date,
                "due_date": due_date,
                "total_amount": total_amount,
                "vendor": vendor_name,
                "items": line_items
            }

        except Exception as e:
            logger.error(f"Error parsing Bova invoice response: {str(e)}")
            return {
                "success": False,
                "error": f"Error parsing invoice: {str(e)}",
                "items": []
            }

    def _parse_kast_response(self, result):
        """
        Parse response from Kast custom API.

        Args:
            result: Result from Mindee API

        Returns:
            dict: Structured data in our application's format
        """
        try:
            # Log the document summary for debugging
            logger.info(f"Kast API response summary: {result.document}")

            # Get the prediction fields
            fields = result.document.inference.prediction.fields

            # Extract invoice data
            invoice_number = self._get_field_value(fields, 'invoice_number')

            # For Kast, the date field is called transaction_date
            date = self._get_field_value(fields, 'transaction_date')
            if not date:
                date = self._get_field_value(fields, 'date')

            due_date = self._get_field_value(fields, 'due_date')

            # For Kast, the total amount field is called invoice_total
            total_amount = self._get_field_value(fields, 'invoice_total')
            if not total_amount:
                total_amount = self._get_field_value(fields, 'total_amount')

            # Default vendor name
            vendor_name = "Kast"

            # Extract line items
            line_items = []

            # Try to get line items from different possible field names
            line_items_field = None

            # Log all available fields for debugging
            logger.info(f"Available fields: {list(fields.keys()) if isinstance(fields, dict) else dir(fields)}")

            for field_name in ['line_items', 'lineItems', 'items']:
                if isinstance(fields, dict) and field_name in fields:
                    line_items_field = fields[field_name]
                    logger.info(f"Found line items in field: {field_name}")
                    break

            # If we didn't find line items in the fields dictionary, try to access it as an attribute
            if line_items_field is None:
                for field_name in ['line_items', 'lineItems', 'items']:
                    if hasattr(fields, field_name):
                        line_items_field = getattr(fields, field_name)
                        logger.info(f"Found line items in attribute: {field_name}")
                        break

            # Process line items if we found them
            if line_items_field:
                # Log the type of line_items_field for debugging
                logger.info(f"Line items field type: {type(line_items_field)}")
                logger.info(f"Line items field value: {line_items_field}")

                # Convert to list if it's not already
                if not isinstance(line_items_field, list):
                    # Try to convert to list if possible
                    try:
                        # If it's a GeneratedV1LineItemField, we need to access its value
                        if hasattr(line_items_field, 'values'):
                            logger.info("Found values attribute in line_items_field")
                            line_items_field = line_items_field.values
                        elif hasattr(line_items_field, 'value'):
                            logger.info("Found value attribute in line_items_field")
                            line_items_field = line_items_field.value

                        # Now try to convert to list
                        if not isinstance(line_items_field, list):
                            line_items_field = list(line_items_field)
                    except Exception as e:
                        logger.warning(f"Could not convert line_items to list: {str(e)}")
                        line_items_field = []

                logger.info(f"Found {len(line_items_field)} line items in Kast response")

                for i, item in enumerate(line_items_field):
                    # For Kast vendor, use the specific field names we've identified from testing
                    # Description: descripiton (note the typo in the API field name)
                    # Unit Price: unit_price
                    # Amount: extended
                    # Quantity: Calculated from amount / unit_price

                    # Get description from descripiton field (primary field for Kast)
                    description = self._get_field_value(item, 'descripiton')  # Note: API has a typo in the field name
                    if not description:
                        # Try alternative field names if the primary one is not available
                        for desc_field in ['description', 'product_name', 'name', 'item_name']:
                            description = self._get_field_value(item, desc_field)
                            if description:
                                logger.info(f"Found description in alternative field: {desc_field}")
                                break

                    # If description is still empty, try to use item_number or label fields
                    if not description:
                        # Try alternative field names that might contain description
                        alt_fields = ['product_description', 'item_description', 'name', 'product_name', 'item_name', 'label']
                        for field in alt_fields:
                            description = self._get_field_value(item, field)
                            if description:
                                break

                    # If still no description, try to use item_number or other identifiers
                    if not description:
                        item_number = self._get_field_value(item, 'item_number')
                        product_code = self._get_field_value(item, 'product_code')
                        sku = self._get_field_value(item, 'sku')

                        if item_number:
                            description = f"Item {item_number}"
                        elif product_code:
                            description = f"Product {product_code}"
                        elif sku:
                            description = f"SKU {sku}"
                        else:
                            # Log this case for debugging and skip items without descriptions
                            logger.warning(f"Kast line item {i+1} has no description or identifiers. Item data: {item}")
                            continue

                    # Get unit price from unit_price field (primary field for Kast)
                    unit_price = self._get_field_value(item, 'unit_price')
                    if unit_price is None:
                        # Try alternative field names if the primary one is not available
                        for price_field in ['price', 'selling_price', 'unit_cost']:
                            unit_price = self._get_field_value(item, price_field)
                            if unit_price is not None:
                                logger.info(f"Found unit price in alternative field: {price_field}")
                                break

                    # Get amount from extended field (primary field for Kast)
                    amount = self._get_field_value(item, 'extended')
                    if amount is None:
                        # Try alternative field names if the primary one is not available
                        for amount_field in ['amount', 'total_amount', 'extensions', 'extension', 'total']:
                            amount = self._get_field_value(item, amount_field)
                            if amount is not None:
                                logger.info(f"Found amount in alternative field: {amount_field}")
                                break

                    # For Kast, we don't have a quantity field, so we'll calculate it from amount / unit_price
                    quantity = None

                    # Convert to appropriate types
                    try:
                        quantity = float(quantity) if quantity else 0
                        unit_price = float(unit_price) if unit_price else 0
                        amount = float(amount) if amount else 0

                        # If quantity is 0 but we have unit_price and amount, calculate quantity
                        if quantity == 0 and unit_price > 0 and amount > 0:
                            # Calculate quantity as amount / unit_price, rounded to nearest integer
                            calculated_quantity = round(amount / unit_price)
                            logger.info(f"Calculated quantity: {calculated_quantity} (amount: {amount} / unit_price: {unit_price})")
                            quantity = calculated_quantity
                    except (ValueError, TypeError):
                        logger.warning(f"Error converting line item values to float: {item}")

                    line_items.append({
                        "description": description,
                        "quantity": quantity,
                        "unit_price": unit_price,
                        "amount": amount
                    })

            logger.info(f"Successfully extracted {len(line_items)} line items from Kast invoice")

            return {
                "success": True,
                "invoice_number": invoice_number,
                "date": date,
                "due_date": due_date,
                "total_amount": total_amount,
                "vendor": vendor_name,
                "items": line_items
            }

        except Exception as e:
            logger.error(f"Error parsing Kast invoice response: {str(e)}")
            return {
                "success": False,
                "error": f"Error parsing invoice: {str(e)}",
                "items": []
            }

    def _parse_dekalb_response(self, result):
        """
        Parse response from Dekalb Produce custom API.

        Args:
            result: Result from Mindee API

        Returns:
            dict: Structured data in our application's format
        """
        try:
            # Log the document summary for debugging
            logger.info(f"Dekalb API response summary: {result.document}")

            # Get the prediction fields
            fields = result.document.inference.prediction.fields

            # Extract invoice data
            invoice_number = self._get_field_value(fields, 'invoice_number')

            # For Dekalb, try different date field names
            date = self._get_field_value(fields, 'invoice_date')
            if not date:
                date = self._get_field_value(fields, 'date')

            due_date = self._get_field_value(fields, 'due_date')

            # For Dekalb, try different total amount field names
            total_amount = self._get_field_value(fields, 'invoice_total')
            if not total_amount:
                total_amount = self._get_field_value(fields, 'total_amount')

            # Default vendor name
            vendor_name = "Dekalb Produce"

            # Extract line items
            line_items = []

            # Try to get line items from different possible field names
            line_items_field = None

            # Log all available fields for debugging
            logger.info(f"Available fields: {list(fields.keys()) if isinstance(fields, dict) else dir(fields)}")

            for field_name in ['line_items', 'lineItems', 'items']:
                if isinstance(fields, dict) and field_name in fields:
                    line_items_field = fields[field_name]
                    logger.info(f"Found line items in field: {field_name}")
                    break

            # If we didn't find line items in the fields dictionary, try to access it as an attribute
            if line_items_field is None:
                for field_name in ['line_items', 'lineItems', 'items']:
                    if hasattr(fields, field_name):
                        line_items_field = getattr(fields, field_name)
                        logger.info(f"Found line items in attribute: {field_name}")
                        break

            # Process line items if we found them
            if line_items_field:
                # Log the type of line_items_field for debugging
                logger.info(f"Line items field type: {type(line_items_field)}")
                logger.info(f"Line items field value: {line_items_field}")

                # Convert to list if it's not already
                if not isinstance(line_items_field, list):
                    # Try to convert to list if possible
                    try:
                        # If it's a GeneratedV1LineItemField, we need to access its value
                        if hasattr(line_items_field, 'values'):
                            logger.info("Found values attribute in line_items_field")
                            line_items_field = line_items_field.values
                        elif hasattr(line_items_field, 'value'):
                            logger.info("Found value attribute in line_items_field")
                            line_items_field = line_items_field.value

                        # Now try to convert to list
                        if not isinstance(line_items_field, list):
                            line_items_field = list(line_items_field)
                    except Exception as e:
                        logger.warning(f"Could not convert line_items to list: {str(e)}")
                        line_items_field = []

                logger.info(f"Found {len(line_items_field)} line items in Dekalb response")

                for i, item in enumerate(line_items_field):
                    # For Dekalb vendor, use the specific field names we've identified from testing
                    # We'll use the same approach as Kast since we don't have specific test data for Dekalb
                    # Description: descripiton (note the typo in the API field name)
                    # Unit Price: unit_price
                    # Amount: extended
                    # Quantity: Calculated from amount / unit_price

                    # Get description from descripiton field (primary field for Dekalb)
                    description = self._get_field_value(item, 'descripiton')  # Note: API has a typo in the field name
                    if not description:
                        # Try alternative field names if the primary one is not available
                        for desc_field in ['description', 'product_name', 'name', 'item_name']:
                            description = self._get_field_value(item, desc_field)
                            if description:
                                logger.info(f"Found description in alternative field: {desc_field}")
                                break

                    # If description is still empty, try to use item_number or label fields
                    if not description:
                        # Try alternative field names that might contain description
                        alt_fields = ['product_description', 'item_description', 'name', 'product_name', 'item_name', 'label']
                        for field in alt_fields:
                            description = self._get_field_value(item, field)
                            if description:
                                break

                    # If still no description, try to use item_number or other identifiers
                    if not description:
                        item_number = self._get_field_value(item, 'item_number')
                        product_code = self._get_field_value(item, 'product_code')
                        sku = self._get_field_value(item, 'sku')

                        if item_number:
                            description = f"Item {item_number}"
                        elif product_code:
                            description = f"Product {product_code}"
                        elif sku:
                            description = f"SKU {sku}"
                        else:
                            # Log this case for debugging and skip items without descriptions
                            logger.warning(f"DeKalb line item {i+1} has no description or identifiers. Item data: {item}")
                            continue

                    # Get unit price from unit_price field (primary field for Dekalb)
                    unit_price = self._get_field_value(item, 'unit_price')
                    if unit_price is None:
                        # Try alternative field names if the primary one is not available
                        for price_field in ['price', 'selling_price', 'unit_cost']:
                            unit_price = self._get_field_value(item, price_field)
                            if unit_price is not None:
                                logger.info(f"Found unit price in alternative field: {price_field}")
                                break

                    # Get amount from extended field (primary field for Dekalb)
                    amount = self._get_field_value(item, 'extended')
                    if amount is None:
                        # Try alternative field names if the primary one is not available
                        for amount_field in ['amount', 'total_amount', 'extensions', 'extension', 'total']:
                            amount = self._get_field_value(item, amount_field)
                            if amount is not None:
                                logger.info(f"Found amount in alternative field: {amount_field}")
                                break

                    # For Dekalb, we'll calculate the quantity from amount / unit_price
                    quantity = None

                    # Convert to appropriate types
                    try:
                        quantity = float(quantity) if quantity else 0
                        unit_price = float(unit_price) if unit_price else 0
                        amount = float(amount) if amount else 0

                        # If quantity is 0 but we have unit_price and amount, calculate quantity
                        if quantity == 0 and unit_price > 0 and amount > 0:
                            # Calculate quantity as amount / unit_price, rounded to nearest integer
                            calculated_quantity = round(amount / unit_price)
                            logger.info(f"Calculated quantity: {calculated_quantity} (amount: {amount} / unit_price: {unit_price})")
                            quantity = calculated_quantity
                    except (ValueError, TypeError):
                        logger.warning(f"Error converting line item values to float: {item}")

                    line_items.append({
                        "description": description,
                        "quantity": quantity,
                        "unit_price": unit_price,
                        "amount": amount
                    })

            logger.info(f"Successfully extracted {len(line_items)} line items from Dekalb invoice")

            return {
                "success": True,
                "invoice_number": invoice_number,
                "date": date,
                "due_date": due_date,
                "total_amount": total_amount,
                "vendor": vendor_name,
                "items": line_items
            }

        except Exception as e:
            logger.error(f"Error parsing Dekalb invoice response: {str(e)}")
            return {
                "success": False,
                "error": f"Error parsing invoice: {str(e)}",
                "items": []
            }

    def _get_field_value(self, fields, field_name):
        """
        Helper method to safely get field values from the Mindee API response.

        Args:
            fields: Dictionary of fields from the Mindee API response
            field_name: Name of the field to get

        Returns:
            The field value, or None if not found
        """
        if not fields:
            return None

        # Handle different types of field containers
        if isinstance(fields, dict):
            field = fields.get(field_name)
        else:
            # Try to access as an attribute
            field = getattr(fields, field_name, None)

        if field is None:
            return None

        # Handle different field types
        if hasattr(field, 'value'):
            return field.value
        elif isinstance(field, dict) and 'value' in field:
            return field['value']
        elif isinstance(field, (str, int, float)):
            return field
        elif isinstance(field, list) and len(field) > 0:
            # For list fields, return the first item
            return field[0]

        return None
